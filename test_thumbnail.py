#!/usr/bin/env python3
"""
缩略图功能测试脚本
"""

import requests
import json
import os
import sys

# 添加后端路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

try:
    from services.thumbnail_service import ThumbnailService
    from services.database_manager import DatabaseManager
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  无法导入后端模块: {e}")
    BACKEND_AVAILABLE = False

def test_thumbnail_service():
    """测试缩略图服务"""
    print("=== 测试缩略图服务 ===")

    if not BACKEND_AVAILABLE:
        print("⚠️  后端模块不可用，跳过缩略图服务测试")
        return

    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        db_manager.init_database()

        # 初始化缩略图服务
        thumbnail_service = ThumbnailService()

        # 测试图片路径（假设存在）
        test_image_paths = [
            "C:/Users/<USER>/Desktop/test.jpg",
            "C:/Users/<USER>/Desktop/test.png",
            "C:/Users/<USER>/Pictures/sample.jpg"
        ]

        for image_path in test_image_paths:
            if os.path.exists(image_path):
                print(f"\n测试图片: {image_path}")

                # 测试生成缩略图
                thumbnail_path = thumbnail_service.generate_thumbnail(image_path, 'medium')
                if thumbnail_path:
                    print(f"✅ 缩略图生成成功: {thumbnail_path}")
                    print(f"   文件大小: {os.path.getsize(thumbnail_path)} bytes")
                else:
                    print(f"❌ 缩略图生成失败")
            else:
                print(f"⚠️  图片文件不存在: {image_path}")
    except Exception as e:
        print(f"❌ 缩略图服务测试失败: {e}")

def test_api_endpoint():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    base_url = "http://localhost:8086"
    
    # 首先登录获取token
    login_data = {
        "username": "test",
        "password": "123456"
    }
    
    try:
        # 登录
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ 登录成功，获取token: {token[:20]}...")
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # 获取文件列表
            files_response = requests.get(f"{base_url}/api/files", headers=headers)
            if files_response.status_code == 200:
                files_data = files_response.json()
                files = files_data.get('files', [])
                print(f"✅ 获取文件列表成功，共 {len(files)} 个文件")
                
                # 测试第一个图片文件的缩略图
                for file in files:
                    if file.get('type') != 'folder':
                        file_id = file.get('id')
                        filename = file.get('name', '')
                        
                        # 检查是否为图片文件
                        ext = filename.lower().split('.')[-1] if '.' in filename else ''
                        if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
                            print(f"\n测试图片文件: {filename} (ID: {file_id})")
                            
                            # 请求缩略图
                            thumbnail_response = requests.get(
                                f"{base_url}/api/files/{file_id}/thumbnail?size=medium",
                                headers=headers
                            )
                            
                            if thumbnail_response.status_code == 200:
                                print(f"✅ 缩略图API调用成功")
                                print(f"   Content-Type: {thumbnail_response.headers.get('Content-Type')}")
                                print(f"   Content-Length: {len(thumbnail_response.content)} bytes")
                            else:
                                print(f"❌ 缩略图API调用失败: {thumbnail_response.status_code}")
                                print(f"   错误信息: {thumbnail_response.text}")
                            
                            break  # 只测试第一个图片文件
                else:
                    print("⚠️  没有找到图片文件进行测试")
            else:
                print(f"❌ 获取文件列表失败: {files_response.status_code}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端正在运行")
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def check_thumbnail_directory():
    """检查缩略图目录"""
    print("\n=== 检查缩略图目录 ===")
    
    thumbnail_dirs = [
        "backend/data/thumbnails",
        "data/thumbnails",
        "thumbnails"
    ]
    
    for thumb_dir in thumbnail_dirs:
        if os.path.exists(thumb_dir):
            print(f"✅ 缩略图目录存在: {thumb_dir}")
            
            # 列出缩略图文件
            try:
                files = os.listdir(thumb_dir)
                print(f"   包含 {len(files)} 个文件")
                
                for file in files[:5]:  # 只显示前5个
                    file_path = os.path.join(thumb_dir, file)
                    size = os.path.getsize(file_path)
                    print(f"   - {file} ({size} bytes)")
                    
                if len(files) > 5:
                    print(f"   ... 还有 {len(files) - 5} 个文件")
                    
            except Exception as e:
                print(f"   ❌ 读取目录失败: {e}")
        else:
            print(f"⚠️  缩略图目录不存在: {thumb_dir}")

if __name__ == "__main__":
    print("缩略图功能测试")
    print("=" * 50)
    
    # 检查缩略图目录
    check_thumbnail_directory()
    
    # 测试缩略图服务
    test_thumbnail_service()
    
    # 测试API端点
    test_api_endpoint()
    
    print("\n测试完成！")

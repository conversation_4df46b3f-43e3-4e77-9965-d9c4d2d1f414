#!/usr/bin/env python3
"""
简单的缩略图功能测试
"""

import os
import sys
from PIL import Image
import requests

def test_pil_thumbnail():
    """测试PIL缩略图生成"""
    print("=== 测试PIL缩略图生成 ===")
    
    # 创建一个测试图片
    test_image_path = "test_image.jpg"
    thumbnail_path = "test_thumbnail.jpg"
    
    try:
        # 创建一个简单的测试图片
        img = Image.new('RGB', (800, 600), color='red')
        img.save(test_image_path, 'JPEG')
        print(f"✅ 创建测试图片: {test_image_path}")
        
        # 生成缩略图
        with Image.open(test_image_path) as image:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 生成缩略图（保持宽高比）
            image.thumbnail((300, 300), Image.Resampling.LANCZOS)
            
            # 保存缩略图
            image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
            
        if os.path.exists(thumbnail_path):
            size = os.path.getsize(thumbnail_path)
            print(f"✅ 缩略图生成成功: {thumbnail_path} ({size} bytes)")
            
            # 验证缩略图
            with Image.open(thumbnail_path) as thumb:
                print(f"   缩略图尺寸: {thumb.size}")
        else:
            print("❌ 缩略图文件未生成")
            
    except Exception as e:
        print(f"❌ PIL缩略图测试失败: {e}")
    finally:
        # 清理测试文件
        for file_path in [test_image_path, thumbnail_path]:
            if os.path.exists(file_path):
                os.remove(file_path)

def test_frontend_thumbnail_request():
    """测试前端缩略图请求"""
    print("\n=== 测试前端缩略图请求 ===")
    
    # 模拟前端请求
    base_url = "http://localhost:8086"
    
    # 测试不同的端点
    endpoints = [
        "/api/files/1/thumbnail?size=medium",
        "/api/files/2/thumbnail?size=small",
        "/api/files/3/thumbnail?size=large"
    ]
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n测试URL: {url}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                content_length = len(response.content)
                print(f"   ✅ 请求成功")
                print(f"   Content-Type: {content_type}")
                print(f"   Content-Length: {content_length} bytes")
            elif response.status_code == 401:
                print(f"   ⚠️  需要认证")
            elif response.status_code == 404:
                print(f"   ⚠️  文件不存在")
            else:
                print(f"   ❌ 请求失败: {response.text[:100]}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败 - 服务器未运行")
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def check_thumbnail_css():
    """检查缩略图CSS样式"""
    print("\n=== 检查缩略图CSS样式 ===")
    
    css_file = "frontend/css/components.css"
    
    if os.path.exists(css_file):
        print(f"✅ CSS文件存在: {css_file}")
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查缩略图相关的CSS类
        thumbnail_classes = [
            '.thumbnail-container',
            '.thumbnail-image',
            '.thumbnail-fallback',
            '.thumbnail-loading',
            '.loading-spinner-small'
        ]
        
        for css_class in thumbnail_classes:
            if css_class in content:
                print(f"   ✅ 找到CSS类: {css_class}")
            else:
                print(f"   ❌ 缺少CSS类: {css_class}")
    else:
        print(f"❌ CSS文件不存在: {css_file}")

def check_frontend_js():
    """检查前端JavaScript"""
    print("\n=== 检查前端JavaScript ===")
    
    js_file = "frontend/js/file-manager.js"
    
    if os.path.exists(js_file):
        print(f"✅ JS文件存在: {js_file}")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查缩略图相关的函数
        thumbnail_functions = [
            'generateThumbnailHTML',
            'preloadThumbnails',
            'refreshThumbnail',
            'getThumbnailURL'
        ]
        
        for func in thumbnail_functions:
            if func in content:
                print(f"   ✅ 找到函数: {func}")
            else:
                print(f"   ❌ 缺少函数: {func}")
                
        # 检查缩略图HTML生成
        if 'thumbnail-container' in content:
            print(f"   ✅ 找到缩略图容器HTML")
        else:
            print(f"   ❌ 缺少缩略图容器HTML")
    else:
        print(f"❌ JS文件不存在: {js_file}")

def main():
    print("缩略图功能简单测试")
    print("=" * 50)
    
    # 测试PIL缩略图生成
    test_pil_thumbnail()
    
    # 检查前端文件
    check_thumbnail_css()
    check_frontend_js()
    
    # 测试API请求
    test_frontend_thumbnail_request()
    
    print("\n测试完成！")
    print("\n建议:")
    print("1. 确保后端服务正在运行")
    print("2. 检查共享文件夹中是否有图片文件")
    print("3. 在浏览器中打开开发者工具查看网络请求")
    print("4. 检查缩略图目录权限")

if __name__ == "__main__":
    main()
